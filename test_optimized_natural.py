#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_optimized_natural_algorithm():
    """测试优化后的自然宽度算法"""
    print("=" * 80)
    print("测试优化后的自然宽度分配算法")
    print("=" * 80)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 重点测试用例
    test_cases = [
        {
            "text": "The power outage lasted hours.",
            "description": "基础测试句子"
        },
        {
            "text": "I rate this book highly.",
            "description": "短单词密度测试"
        },
        {
            "text": "The revolver's double-action trigger.",
            "description": "连字符单词测试（关键）"
        },
        {
            "text": "The microscope has a magnificationpower of 1000x.",
            "description": "图片问题句子"
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        print(f"句子: {case['text']}")
        
        tokens = extract_tokens(case['text'])
        colwidths = app.get_col_widths(tokens)
        
        print(f"分词: {tokens}")
        print("优化后宽度分配:")
        
        total_width = sum(w.cm for w in colwidths)
        
        for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
            width_cm = width.cm
            char_count = len(token)
            char_density = width_cm / char_count if char_count > 0 else 0
            
            # 计算理想密度
            ideal_width = char_count * 0.12 + 0.15
            if char_count == 1:
                ideal_width = max(ideal_width, 0.35)
            elif char_count == 2:
                ideal_width = max(ideal_width, 0.45)
            ideal_density = ideal_width / char_count
            
            # 评估密度一致性
            density_diff = abs(char_density - ideal_density)
            if density_diff <= 0.02:
                status = "✅ 优秀"
            elif density_diff <= 0.05:
                status = "✅ 良好"
            else:
                status = "⚠️  偏差"
            
            print(f"  {i:2d}. {token:18s}: {width_cm:5.2f}cm ({char_count}字符, 密度{char_density:.3f}) {status}")
        
        print(f"总宽度: {total_width:.2f}cm")
        
        # 检查关键指标
        min_width = min(w.cm for w in colwidths)
        max_width = max(w.cm for w in colwidths)
        width_ratio = max_width / min_width
        
        print(f"最小宽度: {min_width:.2f}cm, 最大宽度: {max_width:.2f}cm, 比例: {width_ratio:.1f}:1")
        
        # 检查特殊单词
        for token, width in zip(tokens, colwidths):
            if token == "double-action":
                if width.cm >= 3.2:
                    print(f"✅ double-action: {width.cm:.2f}cm (确保不换行)")
                else:
                    print(f"❌ double-action: {width.cm:.2f}cm (可能换行)")

def test_density_consistency():
    """测试密度一致性改进"""
    print("\n" + "=" * 80)
    print("密度一致性改进测试")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试不同长度的单词
    test_words = ["I", "am", "the", "book", "power", "outage", "trigger", "calculate"]
    
    print("优化后密度分析:")
    print(f"{'单词':<12} {'字符数':<6} {'宽度(cm)':<10} {'密度':<10} {'理想密度':<10} {'差异':<8} {'评价'}")
    print("-" * 75)
    
    densities = []
    density_diffs = []
    
    for word in test_words:
        tokens = [word]
        colwidths = app.get_col_widths(tokens)
        
        if colwidths:
            width_cm = colwidths[0].cm
            char_count = len(word)
            density = width_cm / char_count
            
            # 理想密度
            ideal_width = char_count * 0.12 + 0.15
            if char_count == 1:
                ideal_width = max(ideal_width, 0.35)
            elif char_count == 2:
                ideal_width = max(ideal_width, 0.45)
            ideal_density = ideal_width / char_count
            
            density_diff = abs(density - ideal_density)
            
            densities.append(density)
            density_diffs.append(density_diff)
            
            # 评价
            if density_diff <= 0.02:
                rating = "✅ 优秀"
            elif density_diff <= 0.05:
                rating = "✅ 良好"
            elif density_diff <= 0.1:
                rating = "⚠️  一般"
            else:
                rating = "❌ 偏差大"
            
            print(f"{word:<12} {char_count:<6} {width_cm:<10.2f} {density:<10.3f} {ideal_density:<10.3f} {density_diff:<8.3f} {rating}")
    
    # 统计分析
    if densities and density_diffs:
        avg_density = sum(densities) / len(densities)
        max_density = max(densities)
        min_density = min(densities)
        density_variance = max_density - min_density
        avg_diff = sum(density_diffs) / len(density_diffs)
        
        print(f"\n📊 密度统计:")
        print(f"  平均密度: {avg_density:.3f}cm/字符")
        print(f"  密度范围: {min_density:.3f} - {max_density:.3f}cm/字符")
        print(f"  密度方差: {density_variance:.3f}cm/字符")
        print(f"  平均偏差: {avg_diff:.3f}cm/字符")
        
        # 评估改进效果
        if density_variance <= 0.1:
            print("✅ 密度一致性优秀")
        elif density_variance <= 0.2:
            print("✅ 密度一致性良好")
        else:
            print("⚠️  密度一致性仍需改进")
        
        if avg_diff <= 0.03:
            print("✅ 与理想密度非常接近")
        elif avg_diff <= 0.05:
            print("✅ 与理想密度较接近")
        else:
            print("⚠️  与理想密度有差距")
    
    root.destroy()

def test_visual_effect():
    """测试视觉效果"""
    print("\n" + "=" * 80)
    print("视觉效果测试")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试图片中的问题句子
    problem_text = "The microscope has a magnificationpower of 1000x."
    tokens = extract_tokens(problem_text)
    colwidths = app.get_col_widths(tokens)
    
    print(f"问题句子: {problem_text}")
    print(f"分词结果: {tokens}")
    print()
    
    print("视觉效果分析:")
    total_width = sum(w.cm for w in colwidths)
    
    for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
        width_cm = width.cm
        char_count = len(token)
        
        # 视觉效果评估
        if char_count <= 3 and width_cm <= 1.0:
            visual = "✅ 紧凑不拥挤"
        elif char_count <= 6 and width_cm <= 1.5:
            visual = "✅ 适中美观"
        elif char_count > 10 and width_cm >= 1.5:
            visual = "✅ 长单词充足"
        elif width_cm > 3.0:
            visual = "⚠️  可能过宽"
        elif width_cm < 0.3:
            visual = "⚠️  可能过窄"
        else:
            visual = "✅ 视觉良好"
        
        print(f"  {i}. {token:20s}: {width_cm:5.2f}cm {visual}")
    
    print(f"\n总宽度: {total_width:.2f}cm")
    
    # 与原始问题对比
    print(f"\n🎯 解决效果评估:")
    print("原始问题:")
    print("- magnificationpower 和 1000x 挤在一个很宽的单元格")
    print("- chargescompetitive 单词被挤在一起")
    print("- 整体宽度分配不均匀")
    print()
    print("优化后效果:")
    print(f"- magnificationpower: {colwidths[4].cm:.2f}cm (合理宽度)")
    print(f"- 1000x: {colwidths[6].cm:.2f}cm (适中宽度)")
    print("- 整体宽度分配更加自然均匀")
    
    root.destroy()

if __name__ == "__main__":
    test_optimized_natural_algorithm()
    test_density_consistency()
    test_visual_effect()
    
    print("\n" + "=" * 80)
    print("优化后自然算法总结")
    print("=" * 80)
    print("🎯 优化重点:")
    print("1. 降低短单词最小宽度，提高密度一致性")
    print("2. 确保double-action等关键词不换行")
    print("3. 基于字符数的精确自然宽度计算")
    print("4. 更接近源文档的视觉效果")
    print("\n✅ 如果测试显示良好的密度一致性和自然的视觉效果，")
    print("   说明算法已经成功解决了所有问题！")
