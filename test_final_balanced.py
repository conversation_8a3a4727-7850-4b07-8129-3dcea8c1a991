#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_final_balanced_algorithm():
    """测试最终平衡算法效果"""
    print("=" * 80)
    print("测试最终平衡列宽分配算法")
    print("=" * 80)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试用例
    test_cases = [
        {
            "text": "The power outage lasted hours.",
            "description": "问题句子（之前过度紧凑）",
            "over_compact": 4.94,
            "original": 11.56
        },
        {
            "text": "I rate this book highly.",
            "description": "短单词句子",
            "over_compact": 4.76,
            "original": 10.90
        },
        {
            "text": "The revolver's double-action trigger.",
            "description": "连字符单词测试",
            "over_compact": 7.20,
            "original": 10.43
        },
        {
            "text": "Calculate 5² using the power rule.",
            "description": "上标字符测试",
            "over_compact": 5.96,
            "original": 11.43
        }
    ]
    
    total_original = 0
    total_over_compact = 0
    total_balanced = 0
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        print(f"句子: {case['text']}")
        
        tokens = extract_tokens(case['text'])
        
        try:
            colwidths = app.get_col_widths(tokens)
            
            print(f"分词: {tokens}")
            print("平衡宽度分配:")
            
            balanced_total = sum(w.cm for w in colwidths)
            over_compact_total = case['over_compact']
            original_total = case['original']
            
            for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
                width_cm = width.cm
                char_count = len(token)
                
                # 评估效果
                if char_count <= 3 and 0.8 <= width_cm <= 1.2:
                    status = "✅ 平衡"
                elif char_count == 4 and 1.0 <= width_cm <= 1.3:
                    status = "✅ 平衡"
                elif char_count >= 5 and width_cm >= 1.2:
                    status = "✅ 充足"
                elif '-' in token and width_cm >= 3.5:
                    status = "✅ 特殊"
                elif any(c in token for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉') and width_cm >= 1.0:
                    status = "✅ 上下标"
                else:
                    status = "⚠️  检查"
                
                print(f"  {i:2d}. {token:15s}: {width_cm:5.2f}cm ({char_count}字符) {status}")
            
            print(f"\n📊 三代算法对比:")
            print(f"  原始算法: {original_total:.2f}cm")
            print(f"  过度紧凑: {over_compact_total:.2f}cm")
            print(f"  平衡算法: {balanced_total:.2f}cm")
            
            # 计算改进效果
            vs_original = ((original_total - balanced_total) / original_total) * 100
            vs_compact = ((balanced_total - over_compact_total) / over_compact_total) * 100
            
            print(f"  vs原始: -{vs_original:.1f}% (节省)")
            print(f"  vs紧凑: +{vs_compact:.1f}% (增加间距)")
            
            # 检查关键指标
            min_width = min(w.cm for w in colwidths)
            max_width = max(w.cm for w in colwidths)
            width_ratio = max_width / min_width
            
            print(f"  最小宽度: {min_width:.2f}cm")
            print(f"  最大宽度: {max_width:.2f}cm")
            print(f"  宽度比例: {width_ratio:.1f}:1")
            
            # 检查特殊单词
            for token, width in zip(tokens, colwidths):
                if token == "double-action":
                    if width.cm >= 3.5:
                        print(f"  ✅ double-action: {width.cm:.2f}cm (不换行)")
                    else:
                        print(f"  ❌ double-action: {width.cm:.2f}cm (可能换行)")
                elif any(c in token for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉'):
                    print(f"  ✅ 上下标 {token}: {width.cm:.2f}cm")
            
            total_original += original_total
            total_over_compact += over_compact_total
            total_balanced += balanced_total
            
        except Exception as e:
            print(f"❌ 算法执行出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 总体效果评估
    if total_original > 0:
        overall_vs_original = ((total_original - total_balanced) / total_original) * 100
        overall_vs_compact = ((total_balanced - total_over_compact) / total_over_compact) * 100
        
        print(f"\n" + "=" * 80)
        print("总体效果评估")
        print("=" * 80)
        print(f"所有测试用例总宽度:")
        print(f"  原始算法: {total_original:.2f}cm")
        print(f"  过度紧凑: {total_over_compact:.2f}cm")
        print(f"  平衡算法: {total_balanced:.2f}cm")
        
        print(f"\n📈 改进效果:")
        print(f"  相比原始算法: -{overall_vs_original:.1f}% (节省宽度)")
        print(f"  相比过度紧凑: +{overall_vs_compact:.1f}% (增加间距)")
        
        # 评估是否达到目标
        print(f"\n🎯 目标达成评估:")
        if 30 <= overall_vs_original <= 45:
            print(f"  ✅ 节省宽度目标达成: {overall_vs_original:.1f}% (目标30-45%)")
        else:
            print(f"  ⚠️  节省宽度: {overall_vs_original:.1f}% (目标30-45%)")
        
        if 30 <= overall_vs_compact <= 50:
            print(f"  ✅ 增加间距目标达成: {overall_vs_compact:.1f}% (目标30-50%)")
        else:
            print(f"  ⚠️  增加间距: {overall_vs_compact:.1f}% (目标30-50%)")
    
    root.destroy()

def test_visual_separation():
    """测试视觉分离效果"""
    print("\n" + "=" * 80)
    print("视觉分离效果测试")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试最容易挤在一起的短单词
    test_text = "I am a good student."
    tokens = extract_tokens(test_text)
    colwidths = app.get_col_widths(tokens)
    
    print(f"测试句子: {test_text}")
    print(f"分词结果: {tokens}")
    print()
    
    print("视觉分离检查:")
    all_separated = True
    
    for i, (token, width) in enumerate(zip(tokens, colwidths)):
        width_cm = width.cm
        char_count = len(token)
        
        # 检查是否有足够的视觉分离
        if char_count == 1 and width_cm >= 0.8:
            separation = "✅ 良好"
        elif char_count == 2 and width_cm >= 0.9:
            separation = "✅ 良好"
        elif char_count >= 3 and width_cm >= 1.0:
            separation = "✅ 良好"
        else:
            separation = "❌ 不足"
            all_separated = False
        
        print(f"  {token:10s}: {width_cm:.2f}cm ({char_count}字符) {separation}")
    
    total_width = sum(w.cm for w in colwidths)
    print(f"\n总宽度: {total_width:.2f}cm")
    
    if all_separated:
        print("✅ 所有单词都有良好的视觉分离")
    else:
        print("❌ 部分单词视觉分离不足")
    
    root.destroy()

if __name__ == "__main__":
    test_final_balanced_algorithm()
    test_visual_separation()
    
    print("\n" + "=" * 80)
    print("最终平衡算法测试总结")
    print("=" * 80)
    print("🎯 平衡目标:")
    print("1. 解决过度紧凑问题，确保单词间有清晰分离")
    print("2. 保持相比原始算法30-40%的宽度节省")
    print("3. 维持所有特殊功能（连字符、上下标）")
    print("4. 实现最佳的视觉密度平衡")
    print("\n如果测试显示良好的视觉分离和合理的宽度，说明平衡成功！")
