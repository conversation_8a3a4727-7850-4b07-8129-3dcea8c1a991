#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_final_fixes():
    """最终综合测试：验证所有修复都正常工作"""
    print("=" * 70)
    print("最终综合测试：数字角标和连字符单词修复验证")
    print("=" * 70)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    
    # 测试用例
    test_cases = [
        {
            "text": "Calculate 5² using the power rule.",
            "description": "数字上标测试",
            "expected_tokens": ["5²"],
            "expected_widths": {"5²": "> 1.0cm"}
        },
        {
            "text": "The formula H₂O represents water.",
            "description": "化学式下标测试", 
            "expected_tokens": ["H₂O"],
            "expected_widths": {"H₂O": "> 1.0cm"}
        },
        {
            "text": "The revolver's double-action trigger allows both cocking and firing.",
            "description": "连字符单词测试",
            "expected_tokens": ["double-action"],
            "expected_widths": {"double-action": "> 3.0cm"}
        },
        {
            "text": "The state-of-the-art eco-system provides 5-star service.",
            "description": "复杂连字符组合测试",
            "expected_tokens": ["state-of-the-art", "eco-system", "5-star"],
            "expected_widths": {
                "state-of-the-art": "> 4.0cm",
                "eco-system": "> 2.5cm", 
                "5-star": "> 1.5cm"
            }
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {case['description']} ---")
        print(f"输入: {case['text']}")
        
        # 分词测试
        tokens = extract_tokens(case['text'])
        print(f"分词结果: {tokens}")
        
        # 检查期望的tokens是否存在
        tokenization_passed = True
        for expected_token in case['expected_tokens']:
            if expected_token in tokens:
                print(f"✅ 期望token '{expected_token}' 正确识别")
            else:
                print(f"❌ 期望token '{expected_token}' 未找到")
                tokenization_passed = False
                all_passed = False
        
        # 宽度测试
        colwidths = app.get_col_widths(tokens)
        width_passed = True
        
        for j, token in enumerate(tokens):
            if token in case['expected_widths']:
                width_cm = colwidths[j].cm
                expected_condition = case['expected_widths'][token]
                
                if expected_condition.startswith("> "):
                    min_width = float(expected_condition[2:-2])  # 去掉 "> " 和 "cm"
                    if width_cm > min_width:
                        print(f"✅ '{token}' 宽度: {width_cm:.2f}cm (满足 {expected_condition})")
                    else:
                        print(f"❌ '{token}' 宽度: {width_cm:.2f}cm (不满足 {expected_condition})")
                        width_passed = False
                        all_passed = False
        
        # 检查是否有孤立的连字符或上标下标
        isolated_issues = []
        if '-' in tokens:
            isolated_issues.append("孤立连字符")
        
        superscript_chars = ['²', '³', '¹', '⁰', '⁴', '⁵', '⁶', '⁷', '⁸', '⁹']
        subscript_chars = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉']
        
        for char in superscript_chars + subscript_chars:
            if char in tokens:
                isolated_issues.append(f"孤立{char}")
        
        if isolated_issues:
            print(f"⚠️  发现问题: {', '.join(isolated_issues)}")
            all_passed = False
        else:
            print("✅ 无孤立字符问题")
        
        test_result = "✅ 通过" if (tokenization_passed and width_passed and not isolated_issues) else "❌ 失败"
        print(f"测试结果: {test_result}")
    
    # 总结
    print("\n" + "=" * 70)
    print("最终测试总结")
    print("=" * 70)
    
    if all_passed:
        print("🎉 所有测试通过！修复完全成功！")
        print("\n✅ 修复成果:")
        print("  • 数字上标下标格式完全保持（如 5², H₂O）")
        print("  • 连字符单词正确识别和显示（如 double-action）")
        print("  • 表格单元格宽度自适应，避免强制换行")
        print("  • 复杂组合场景处理正确")
        print("  • 无孤立字符问题")
        
        print("\n🔧 技术改进:")
        print("  • 改进的分词正则表达式，支持上标下标字符")
        print("  • 增强的表格宽度计算，特别照顾连字符单词")
        print("  • 添加了禁止单元格内文本换行的XML设置")
        print("  • 精确的字符定位和格式保持机制")
        
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    root.destroy()
    return all_passed

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 70)
    print("边界情况测试")
    print("=" * 70)
    
    edge_cases = [
        "x³ + y² = z¹",  # 多个上标
        "CO₂ and H₂SO₄",  # 多个化学式
        "twenty-first-century state-of-the-art technology",  # 超长连字符
        "5² × 10³ = 2.5 × 10⁵",  # 科学计数法
    ]
    
    for case in edge_cases:
        print(f"\n边界测试: {case}")
        tokens = extract_tokens(case)
        print(f"分词结果: {tokens}")
        
        # 检查复杂字符是否被正确处理
        complex_tokens = [t for t in tokens if any(c in t for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉-')]
        if complex_tokens:
            print(f"✅ 复杂token: {complex_tokens}")
        else:
            print("⚠️  未发现复杂token")

if __name__ == "__main__":
    success = test_final_fixes()
    test_edge_cases()
    
    if success:
        print("\n🎯 修复验证完成：所有问题已解决！")
        print("现在可以正常处理包含数字角标和连字符单词的文档了。")
    else:
        print("\n⚠️  仍有问题需要解决，请检查失败的测试用例。")
