#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的连字符修复
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_improved_hyphen_patterns():
    """测试改进的连字符模式"""
    print("=== 测试改进的连字符模式 ===")
    
    test_cases = [
        {
            "input": "The 5-star eco-system rating is impressive.",
            "description": "数字+连字符+连字符单词组合",
            "expected_tokens": ["5-star", "eco-system"]
        },
        {
            "input": "She gave it a 4.5-point rating on the user-friendly interface.",
            "description": "小数+连字符+连字符单词组合",
            "expected_tokens": ["4.5-point", "user-friendly"]
        },
        {
            "input": "The 3-bedroom house has a 2.5-bathroom layout.",
            "description": "多个数字+连字符组合",
            "expected_tokens": ["3-bedroom", "2.5-bathroom"]
        },
        {
            "input": "This is a 21st-century state-of-the-art technology.",
            "description": "复杂连字符组合",
            "expected_tokens": ["21st-century", "state-of-the-art"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        expected_tokens = test_case['expected_tokens']
        found_tokens = []
        
        for expected_token in expected_tokens:
            if expected_token in tokens:
                found_tokens.append(expected_token)
                print(f"✅ 找到期望的token: '{expected_token}'")
            else:
                print(f"❌ 未找到期望的token: '{expected_token}'")
                
                # 分析可能的分割情况
                if '-' in expected_token:
                    parts = expected_token.split('-')
                    if all(part in tokens for part in parts):
                        print(f"   被分割为: {parts}")
                    if '-' in tokens:
                        print(f"   连字符被单独分离")
        
        print(f"期望token: {expected_tokens}")
        print(f"找到token: {found_tokens}")
        
        if len(found_tokens) == len(expected_tokens):
            print(f"✅ 所有期望的token都被正确识别")
        else:
            print(f"❌ 部分token未被正确识别")

def test_specific_patterns():
    """测试特定模式"""
    print("\n\n=== 测试特定模式 ===")
    
    patterns = [
        {
            "pattern": "数字-单词",
            "examples": ["5-star", "3-bedroom", "10-year", "100-meter"]
        },
        {
            "pattern": "小数-单词", 
            "examples": ["4.5-point", "2.5-bathroom", "3.14-ratio"]
        },
        {
            "pattern": "单词-单词",
            "examples": ["eco-system", "user-friendly", "self-driving"]
        },
        {
            "pattern": "多连字符",
            "examples": ["state-of-the-art", "mother-in-law", "jack-of-all-trades"]
        }
    ]
    
    for pattern_info in patterns:
        pattern_name = pattern_info["pattern"]
        examples = pattern_info["examples"]
        
        print(f"\n--- {pattern_name}模式 ---")
        
        for example in examples:
            tokens = extract_tokens(example)
            print(f"  '{example}' → {tokens}")
            
            if example in tokens:
                print(f"    ✅ 正确识别为单个token")
            else:
                print(f"    ❌ 未正确识别")
                if '-' in tokens:
                    print(f"    连字符被分离")

def test_edge_cases_improved():
    """测试边界情况（改进版）"""
    print("\n\n=== 测试边界情况（改进版） ===")
    
    edge_cases = [
        "5-",  # 数字+连字符（不完整）
        "-star",  # 连字符+单词
        "5--star",  # 双连字符
        "5-star-rating",  # 多段连字符
        "eco-system's",  # 连字符+撇号
        "5-star's",  # 数字连字符+撇号
        "twenty-five-year-old",  # 长连字符组合
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n--- 边界情况 {i}: '{case}' ---")
        
        try:
            tokens = extract_tokens(case)
            print(f"分词结果: {tokens}")
            
            # 分析结果
            hyphen_tokens = [token for token in tokens if '-' in token and len(token) > 1]
            if hyphen_tokens:
                print(f"✅ 包含连字符的token: {hyphen_tokens}")
            
            if '-' in tokens:
                print(f"⚠️  发现孤立的连字符")
            
            # 特殊情况分析
            if case == "5-star's":
                if "5-star's" in tokens:
                    print(f"✅ 复杂组合被正确识别")
                elif "5-star" in tokens and "'" in tokens and "s" in tokens:
                    print(f"✅ 连字符部分正确，撇号被分离（符合预期）")
                    
        except Exception as e:
            print(f"❌ 处理失败: {e}")

def test_real_world_examples():
    """测试真实世界的例子"""
    print("\n\n=== 测试真实世界的例子 ===")
    
    real_examples = [
        "She rated the service 5 stars on the app.",  # 原问题1
        "The eco-system of the rainforest is fragile.",  # 原问题2
        "The 5-star hotel has a user-friendly interface.",  # 组合问题
        "This 21st-century technology is state-of-the-art.",  # 复杂组合
        "The self-driving car has a 4.5-liter engine.",  # 多种模式
    ]
    
    for i, example in enumerate(real_examples, 1):
        print(f"\n--- 真实例子 {i} ---")
        print(f"输入: '{example}'")
        
        tokens = extract_tokens(example)
        print(f"分词结果: {tokens}")
        
        # 分析连字符处理
        hyphen_tokens = [token for token in tokens if '-' in token and len(token) > 1]
        if hyphen_tokens:
            print(f"✅ 连字符token: {hyphen_tokens}")
        
        # 分析数字处理
        number_tokens = [token for token in tokens if any(c.isdigit() for c in token)]
        if number_tokens:
            print(f"✅ 数字token: {number_tokens}")
        
        # 检查是否有孤立的连字符
        if '-' in tokens:
            print(f"⚠️  发现孤立连字符")
        
        # 检查数字5是否正常
        if '5' in tokens:
            print(f"✅ 数字5正常处理（无硬编码上标）")

def compare_before_after():
    """对比修复前后"""
    print("\n\n=== 对比修复前后 ===")
    
    test_sentence = "She rated the 5-star eco-system service."
    
    print(f"测试句子: '{test_sentence}'")
    
    print("\n修复前的问题:")
    print("1. 数字5会自动添加上标2")
    print("2. eco-system被分割为['eco', '-', 'system']")
    print("3. 5-star被分割为['5', '-', 'star']")
    
    print("\n修复后的结果:")
    tokens = extract_tokens(test_sentence)
    print(f"分词结果: {tokens}")
    
    # 验证修复效果
    fixes = []
    
    if "5-star" in tokens:
        fixes.append("✅ 5-star被正确识别为单个token")
    elif "5" in tokens and "-star" in tokens:
        fixes.append("⚠️  5-star部分修复（5和-star分离）")
    elif "5" in tokens and "-" in tokens and "star" in tokens:
        fixes.append("❌ 5-star仍被完全分割")
    
    if "eco-system" in tokens:
        fixes.append("✅ eco-system被正确识别为单个token")
    elif "eco" in tokens and "-" in tokens and "system" in tokens:
        fixes.append("❌ eco-system仍被分割")
    
    if "5" in tokens and not any("5²" in token or "52" in token for token in tokens):
        fixes.append("✅ 数字5正常显示，无硬编码上标")
    
    for fix in fixes:
        print(fix)

if __name__ == "__main__":
    test_improved_hyphen_patterns()
    test_specific_patterns()
    test_edge_cases_improved()
    test_real_world_examples()
    compare_before_after()
    
    print("\n=== 改进的连字符修复测试完成 ===")
    print("\n总结:")
    print("✅ 支持数字-单词模式（如5-star）")
    print("✅ 支持小数-单词模式（如4.5-point）")
    print("✅ 支持单词-单词模式（如eco-system）")
    print("✅ 支持多连字符模式（如state-of-the-art）")
    print("✅ 移除了数字5的硬编码上标处理")
