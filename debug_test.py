#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from oringin import extract_tokens

# 测试连字符单词
text1 = "The revolver's double-action trigger allows both cocking and firing."
tokens1 = extract_tokens(text1)
print("Test 1 - 连字符单词:")
print(f"输入: {text1}")
print(f"分词结果: {tokens1}")
print(f"double-action 是否完整: {'double-action' in tokens1}")
print()

# 测试数字5
text2 = "Calculate 5 using the power rule."
tokens2 = extract_tokens(text2)
print("Test 2 - 数字5:")
print(f"输入: {text2}")
print(f"分词结果: {tokens2}")
print(f"数字5的位置: {tokens2.index('5') if '5' in tokens2 else '未找到'}")
print()

# 测试表格宽度
import tkinter as tk
from oringin import WordAssistantApp

root = tk.Tk()
root.withdraw()
app = WordAssistantApp(root)

# 测试 double-action 的宽度
test_tokens = ['double-action']
colwidths = app.get_col_widths(test_tokens)
print("Test 3 - 列宽:")
print(f"double-action 的宽度: {colwidths[0].cm:.2f}cm")

root.destroy()
