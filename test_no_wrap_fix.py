#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_double_action_width():
    """专门测试double-action的宽度和防换行设置"""
    print("=" * 70)
    print("测试 double-action 防换行修复")
    print("=" * 70)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)  # 设置默认最大宽度
    
    # 测试句子
    test_text = "The revolver's double-action trigger allows both cocking and firing."
    tokens = extract_tokens(test_text)
    colwidths = app.get_col_widths(tokens)
    
    print(f"测试句子: {test_text}")
    print(f"分词结果: {tokens}")
    print()
    
    # 找到double-action的宽度
    double_action_width = None
    double_action_index = None
    
    for i, token in enumerate(tokens):
        if token == "double-action":
            double_action_width = colwidths[i].cm
            double_action_index = i
            break
    
    if double_action_width:
        print(f"🎯 关键结果:")
        print(f"   double-action 宽度: {double_action_width:.2f}cm")
        
        # 评估宽度是否足够
        if double_action_width >= 4.0:
            print(f"   ✅ 宽度充足 (>= 4.0cm) - 应该能避免换行")
        elif double_action_width >= 3.5:
            print(f"   ⚠️  宽度一般 (>= 3.5cm) - 可能仍会换行")
        else:
            print(f"   ❌ 宽度不足 (< 3.5cm) - 很可能换行")
        
        # 检查是否超过最大宽度限制
        if double_action_width > 6.0:
            print(f"   ⚠️  宽度过大 (> 6.0cm) - 可能导致表格拉伸")
        
    else:
        print("❌ 未找到 double-action token")
    
    print(f"\n📊 完整宽度分配:")
    for i, (token, width) in enumerate(zip(tokens, colwidths)):
        width_cm = width.cm
        if token == "double-action":
            print(f"   {i+1:2d}. {token:15s}: {width_cm:5.2f}cm ← 🎯 目标单词")
        else:
            print(f"   {i+1:2d}. {token:15s}: {width_cm:5.2f}cm")
    
    total_width = sum(w.cm for w in colwidths)
    print(f"\n📏 总宽度: {total_width:.2f}cm")
    
    root.destroy()

def test_other_hyphen_words():
    """测试其他连字符单词"""
    print("\n" + "=" * 70)
    print("测试其他连字符单词的宽度")
    print("=" * 70)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    test_cases = [
        "state-of-the-art",
        "well-known", 
        "user-friendly",
        "high-quality",
        "self-driving",
        "twenty-first-century"
    ]
    
    for word in test_cases:
        tokens = extract_tokens(word)
        colwidths = app.get_col_widths(tokens)
        
        if tokens and tokens[0] == word:
            width_cm = colwidths[0].cm
            print(f"{word:20s}: {width_cm:5.2f}cm", end="")
            
            if width_cm >= 4.0:
                print(" ✅ 充足")
            elif width_cm >= 3.0:
                print(" ⚠️  一般") 
            else:
                print(" ❌ 不足")
        else:
            print(f"{word:20s}: 分词失败 - {tokens}")
    
    root.destroy()

def test_prevention_settings():
    """测试防换行设置的逻辑"""
    print("\n" + "=" * 70)
    print("测试防换行设置逻辑")
    print("=" * 70)
    
    test_words = [
        ("double-action", True, "长连字符单词"),
        ("well-known", True, "中等连字符单词"),
        ("5-star", False, "短连字符单词"),
        ("5²", True, "包含上标"),
        ("H₂O", True, "包含下标"),
        ("normal", False, "普通单词"),
        ("the", False, "短单词")
    ]
    
    print("单词测试 (是否应该设置防换行):")
    for word, should_prevent, description in test_words:
        # 检查逻辑
        has_hyphen = '-' in word and len(word) > 6
        has_special = any(c in word for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉')
        will_prevent = has_hyphen or has_special
        
        status = "✅" if will_prevent == should_prevent else "❌"
        print(f"  {status} {word:15s}: {will_prevent} (期望: {should_prevent}) - {description}")

if __name__ == "__main__":
    test_double_action_width()
    test_other_hyphen_words()
    test_prevention_settings()
    
    print("\n" + "=" * 70)
    print("修复总结")
    print("=" * 70)
    print("🔧 应用的修复:")
    print("  1. 大幅增加连字符单词宽度 (每个连字符+0.25cm)")
    print("  2. 放宽最大宽度限制 (最多6cm)")
    print("  3. 添加多层防换行设置:")
    print("     - 单元格级别: w:noWrap")
    print("     - 段落级别: w:keepLines")
    print("     - 禁用自动连字符: w:suppressAutoHyphens")
    print("\n如果 double-action 宽度 >= 4.0cm，应该能解决换行问题！")
