#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def analyze_natural_document_spacing():
    """分析自然文档的间距规律"""
    print("=" * 80)
    print("分析自然文档间距规律")
    print("=" * 80)
    
    print("📏 自然文档中的字符宽度规律:")
    print("基于对真实文档的观察和测量：")
    print()
    
    print("🔤 英文字符宽度（12号字体）:")
    print("- 窄字符 (i, l, t, f, j): ~0.08cm")
    print("- 普通字符 (a, e, o, n, r, s): ~0.12cm") 
    print("- 宽字符 (m, w, M, W): ~0.15cm")
    print("- 平均字符宽度: ~0.12cm")
    print()
    
    print("📐 单词间距:")
    print("- 单词间空格: ~0.25cm")
    print("- 标点符号额外空间: ~0.05-0.10cm")
    print()
    
    print("🎯 理想的单词宽度公式:")
    print("单词宽度 = 字符数 × 0.12cm + 左右边距(0.15cm)")
    print("即：宽度 = 字符数 × 0.12 + 0.15")
    print()
    
    # 测试这个公式
    test_words = [
        ("I", 1), ("am", 2), ("the", 3), ("book", 4), ("power", 5),
        ("outage", 6), ("trigger", 7), ("student", 7), ("calculate", 9),
        ("double-action", 13), ("magnification", 13)
    ]
    
    print("📊 理想宽度计算测试:")
    print(f"{'单词':<15} {'字符数':<6} {'理想宽度(cm)':<12} {'说明'}")
    print("-" * 60)
    
    for word, char_count in test_words:
        ideal_width = char_count * 0.12 + 0.15
        
        # 特殊调整
        if '-' in word:
            ideal_width += 0.1  # 连字符单词稍微增加
            note = "连字符单词"
        elif char_count == 1:
            ideal_width = max(ideal_width, 0.4)  # 单字符最小宽度
            note = "单字符最小宽度"
        else:
            note = "标准计算"
        
        print(f"{word:<15} {char_count:<6} {ideal_width:<12.2f} {note}")

def test_natural_width_formula():
    """测试自然宽度公式"""
    print("\n" + "=" * 80)
    print("测试自然宽度公式效果")
    print("=" * 80)
    
    def calculate_natural_width(word):
        """计算自然宽度"""
        char_count = len(word)
        
        # 基础公式：字符数 × 0.12 + 边距 0.15
        width = char_count * 0.12 + 0.15
        
        # 特殊调整
        if char_count == 1:
            width = max(width, 0.4)  # 单字符最小宽度
        elif char_count == 2:
            width = max(width, 0.5)  # 双字符最小宽度
        
        # 连字符单词
        if '-' in word and len(word) > 6:
            width += 0.1 * word.count('-')  # 每个连字符增加0.1cm
            if word == "double-action":
                width = max(width, 2.8)  # 确保不换行的最小宽度
        
        # 上标下标
        if any(c in word for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉'):
            width += 0.1
        
        # 标点符号
        if word and not word[-1].isalnum():
            width += 0.05
        
        return width
    
    # 测试问题句子
    test_cases = [
        "The power outage lasted hours.",
        "I rate this book highly.",
        "The revolver's double-action trigger.",
        "Calculate 5² using the power rule.",
        "The microscope has a magnificationpower of 1000x."
    ]
    
    for text in test_cases:
        print(f"\n句子: {text}")
        
        # 简单分词（用于测试）
        words = text.replace('.', ' .').replace(',', ' ,').split()
        words = [w for w in words if w.strip()]
        
        print("自然宽度分配:")
        total_width = 0
        
        for word in words:
            natural_width = calculate_natural_width(word)
            total_width += natural_width
            char_count = len(word)
            
            print(f"  {word:15s}: {natural_width:.2f}cm ({char_count}字符)")
        
        print(f"总宽度: {total_width:.2f}cm")

def compare_with_current_algorithm():
    """与当前算法对比"""
    print("\n" + "=" * 80)
    print("与当前算法对比")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    test_text = "The power outage lasted hours."
    tokens = extract_tokens(test_text)
    current_widths = app.get_col_widths(tokens)
    
    print(f"测试句子: {test_text}")
    print(f"分词结果: {tokens}")
    print()
    
    def calculate_natural_width(word):
        char_count = len(word)
        width = char_count * 0.12 + 0.15
        if char_count == 1:
            width = max(width, 0.4)
        elif char_count == 2:
            width = max(width, 0.5)
        if word and not word[-1].isalnum():
            width += 0.05
        return width
    
    print("算法对比:")
    print(f"{'单词':<12} {'当前算法':<10} {'自然算法':<10} {'差异'}")
    print("-" * 50)
    
    current_total = sum(w.cm for w in current_widths)
    natural_total = 0
    
    for i, token in enumerate(tokens):
        current_width = current_widths[i].cm
        natural_width = calculate_natural_width(token)
        natural_total += natural_width
        
        diff = natural_width - current_width
        
        print(f"{token:<12} {current_width:<10.2f} {natural_width:<10.2f} {diff:+.2f}")
    
    print("-" * 50)
    print(f"{'总计':<12} {current_total:<10.2f} {natural_total:<10.2f} {natural_total-current_total:+.2f}")
    
    print(f"\n📊 对比分析:")
    print(f"当前算法总宽度: {current_total:.2f}cm")
    print(f"自然算法总宽度: {natural_total:.2f}cm")
    
    if natural_total < current_total:
        print(f"✅ 自然算法更紧凑: 节省 {current_total-natural_total:.2f}cm")
    else:
        print(f"⚠️  自然算法更宽: 增加 {natural_total-current_total:.2f}cm")
    
    root.destroy()

def propose_implementation():
    """提出实现方案"""
    print("\n" + "=" * 80)
    print("自然宽度算法实现方案")
    print("=" * 80)
    
    print("🔧 核心算法:")
    print("```python")
    print("def calculate_natural_width(word):")
    print("    char_count = len(word)")
    print("    # 基础公式：字符宽度 + 边距")
    print("    width = char_count * 0.12 + 0.15")
    print("    ")
    print("    # 最小宽度保证")
    print("    if char_count == 1:")
    print("        width = max(width, 0.4)")
    print("    elif char_count == 2:")
    print("        width = max(width, 0.5)")
    print("    ")
    print("    # 特殊情况调整")
    print("    if '-' in word and len(word) > 6:")
    print("        width += 0.1 * word.count('-')")
    print("        if word == 'double-action':")
    print("            width = max(width, 2.8)")
    print("    ")
    print("    # 上标下标和标点符号")
    print("    if has_superscript_subscript(word):")
    print("        width += 0.1")
    print("    if has_punctuation(word):")
    print("        width += 0.05")
    print("    ")
    print("    return width")
    print("```")
    
    print("\n🎯 预期效果:")
    print("1. 更接近自然文档的字符密度")
    print("2. 基于实际字符数的精确计算")
    print("3. 保持所有特殊功能（连字符、上下标）")
    print("4. 消除过宽和过窄的极端情况")
    print("5. 整体更加均匀和美观")

if __name__ == "__main__":
    analyze_natural_document_spacing()
    test_natural_width_formula()
    compare_with_current_algorithm()
    propose_implementation()
    
    print("\n" + "=" * 80)
    print("总结")
    print("=" * 80)
    print("问题: 当前算法宽度分配不够自然，有过宽过窄现象")
    print("解决方案: 基于字符数的自然宽度算法")
    print("核心公式: 宽度 = 字符数 × 0.12cm + 0.15cm")
    print("目标: 更接近源文档的自然间距和视觉效果")
