#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from oringin import extract_tokens

# 测试1：连字符单词分词
text1 = "The revolver's double-action trigger allows both cocking and firing."
tokens1 = extract_tokens(text1)
print("=== 测试连字符单词分词 ===")
print(f"输入: {text1}")
print(f"分词结果: {tokens1}")
print(f"double-action是否完整: {'double-action' in tokens1}")
if 'double-action' not in tokens1:
    print("❌ 连字符单词被错误分割")
    # 查找相关tokens
    related = [t for t in tokens1 if 'double' in t or 'action' in t or '-' in t]
    print(f"相关tokens: {related}")
else:
    print("✅ 连字符单词正确识别")
print()

# 测试2：数字5的处理
text2 = "Calculate 5 using the power rule."
tokens2 = extract_tokens(text2)
print("=== 测试数字5分词 ===")
print(f"输入: {text2}")
print(f"分词结果: {tokens2}")
print(f"数字5: {'5' in tokens2}")
print()

# 测试3：表格宽度计算
import tkinter as tk
from oringin import WordAssistantApp

root = tk.Tk()
root.withdraw()
app = WordAssistantApp(root)

print("=== 测试表格宽度计算（修复后）===")
test_cases = [
    ['double-action'],
    ['double', '-', 'action'],  # 如果被分割的情况
    ['The', 'revolver\'s', 'double-action', 'trigger'],
    ['state-of-the-art'],  # 超长连字符单词
    ['well-known'],  # 中等连字符单词
]

for i, tokens in enumerate(test_cases, 1):
    colwidths = app.get_col_widths(tokens)
    print(f"测试{i}: {tokens}")
    for j, (token, width) in enumerate(zip(tokens, colwidths)):
        width_cm = width.cm
        print(f"  {token}: {width_cm:.2f}cm", end="")
        if '-' in token and len(token) > 6:
            print(f" (连字符单词，长度{len(token)})")
        else:
            print()
    print()

root.destroy()
