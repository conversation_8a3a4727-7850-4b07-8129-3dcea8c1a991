#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_compact_algorithm():
    """测试紧凑化算法效果"""
    print("=" * 80)
    print("测试紧凑化列宽分配算法")
    print("=" * 80)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试用例
    test_cases = [
        {
            "text": "I rate this book highly.",
            "description": "短单词句子（主要优化目标）",
            "old_total": 10.90  # 之前测试的结果
        },
        {
            "text": "The power outage lasted hours.",
            "description": "混合长度单词",
            "old_total": 11.56
        },
        {
            "text": "The revolver's double-action trigger.",
            "description": "包含长连字符单词",
            "old_total": 10.43
        },
        {
            "text": "Calculate 5² using the power rule.",
            "description": "包含上标的句子",
            "old_total": 11.43
        }
    ]
    
    total_old_width = 0
    total_new_width = 0
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        print(f"句子: {case['text']}")
        
        tokens = extract_tokens(case['text'])
        
        try:
            colwidths = app.get_col_widths(tokens)
            
            print(f"分词: {tokens}")
            print("紧凑化宽度分配:")
            
            new_total = sum(w.cm for w in colwidths)
            old_total = case['old_total']
            savings = old_total - new_total
            savings_percent = (savings / old_total) * 100
            
            for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
                width_cm = width.cm
                char_count = len(token)
                char_density = width_cm / char_count if char_count > 0 else 0
                
                # 评估紧凑度
                if char_count <= 3 and width_cm <= 0.8:
                    status = "✅ 紧凑"
                elif char_count <= 4 and width_cm <= 1.0:
                    status = "✅ 合理"
                elif char_count > 6 and width_cm >= 1.5:
                    status = "✅ 充足"
                elif '-' in token and width_cm >= 3.0:
                    status = "✅ 特殊"
                else:
                    status = "⚠️  检查"
                
                print(f"  {i:2d}. {token:15s}: {width_cm:5.2f}cm ({char_count}字符) {status}")
            
            print(f"\n📊 效果对比:")
            print(f"  旧算法总宽度: {old_total:.2f}cm")
            print(f"  新算法总宽度: {new_total:.2f}cm")
            print(f"  节省宽度: {savings:.2f}cm ({savings_percent:.1f}%)")
            
            # 检查关键指标
            min_width = min(w.cm for w in colwidths)
            max_width = max(w.cm for w in colwidths)
            width_ratio = max_width / min_width
            
            print(f"  最小宽度: {min_width:.2f}cm")
            print(f"  最大宽度: {max_width:.2f}cm")
            print(f"  宽度比例: {width_ratio:.1f}:1")
            
            # 检查double-action（如果存在）
            for token, width in zip(tokens, colwidths):
                if token == "double-action":
                    if width.cm >= 3.5:
                        print(f"  ✅ double-action: {width.cm:.2f}cm (足够避免换行)")
                    else:
                        print(f"  ❌ double-action: {width.cm:.2f}cm (可能换行)")
                    break
            
            total_old_width += old_total
            total_new_width += new_total
            
        except Exception as e:
            print(f"❌ 算法执行出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 总体效果
    if total_old_width > 0:
        total_savings = total_old_width - total_new_width
        total_savings_percent = (total_savings / total_old_width) * 100
        
        print(f"\n" + "=" * 80)
        print("总体优化效果")
        print("=" * 80)
        print(f"所有测试用例总宽度:")
        print(f"  优化前: {total_old_width:.2f}cm")
        print(f"  优化后: {total_new_width:.2f}cm")
        print(f"  总节省: {total_savings:.2f}cm ({total_savings_percent:.1f}%)")
        
        if total_savings_percent >= 20:
            print("✅ 优化效果显著！")
        elif total_savings_percent >= 10:
            print("⚠️  优化效果一般")
        else:
            print("❌ 优化效果不明显")
    
    root.destroy()

def test_specific_short_words():
    """专门测试短单词的紧凑效果"""
    print("\n" + "=" * 80)
    print("短单词紧凑效果专项测试")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 专门测试短单词
    short_words = ["I", "a", "an", "to", "of", "in", "on", "at", "by", "for", "the", "and", "but", "or"]
    
    print("短单词宽度测试:")
    print(f"{'单词':<8} {'字符数':<6} {'宽度(cm)':<10} {'密度':<12} {'评价'}")
    print("-" * 50)
    
    for word in short_words:
        tokens = [word]  # 单个单词测试
        colwidths = app.get_col_widths(tokens)
        
        if colwidths:
            width_cm = colwidths[0].cm
            char_count = len(word)
            density = width_cm / char_count
            
            # 评价标准
            if char_count == 1 and width_cm <= 0.7:
                rating = "✅ 优秀"
            elif char_count == 2 and width_cm <= 0.8:
                rating = "✅ 优秀"
            elif char_count == 3 and width_cm <= 0.9:
                rating = "✅ 优秀"
            elif width_cm <= 1.0:
                rating = "✅ 良好"
            elif width_cm <= 1.2:
                rating = "⚠️  一般"
            else:
                rating = "❌ 过宽"
            
            print(f"{word:<8} {char_count:<6} {width_cm:<10.2f} {density:<12.3f} {rating}")
    
    root.destroy()

if __name__ == "__main__":
    test_compact_algorithm()
    test_specific_short_words()
    
    print("\n" + "=" * 80)
    print("紧凑化算法测试总结")
    print("=" * 80)
    print("🎯 优化目标:")
    print("1. 短单词更紧凑，减少不必要空白")
    print("2. 保持长单词和特殊单词足够宽度")
    print("3. 整体节省20-30%的表格宽度")
    print("4. 提升视觉密度，更接近自然文档")
    print("\n如果测试显示显著的宽度节省，说明紧凑化成功！")
