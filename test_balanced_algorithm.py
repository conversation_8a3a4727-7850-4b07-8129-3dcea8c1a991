#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_balanced_algorithm():
    """测试新的平衡宽度分配算法"""
    print("=" * 80)
    print("测试新的平衡宽度分配算法")
    print("=" * 80)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试句子
    test_text = "The revolver's double-action trigger allows both cocking and firing in one motion."
    tokens = extract_tokens(test_text)
    
    print(f"测试句子: {test_text}")
    print(f"分词结果: {tokens}")
    print(f"Token数量: {len(tokens)}")
    print()
    
    try:
        # 测试新算法
        colwidths = app.get_col_widths(tokens)
        
        print("🎯 新算法结果:")
        print(f"{'序号':<4} {'Token':<20} {'宽度(cm)':<10} {'类型'}")
        print("-" * 60)
        
        total_width = sum(w.cm for w in colwidths)
        min_width = min(w.cm for w in colwidths)
        max_width = max(w.cm for w in colwidths)
        
        for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
            width_cm = width.cm
            
            # 判断类型
            if '-' in token and len(token) > 6:
                token_type = "特殊(连字符)"
            elif any(c in token for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉'):
                token_type = "特殊(上下标)"
            else:
                token_type = "普通"
            
            print(f"{i:<4} {token:<20} {width_cm:<10.2f} {token_type}")
        
        print("-" * 60)
        print(f"总宽度: {total_width:.2f}cm")
        print(f"最小宽度: {min_width:.2f}cm")
        print(f"最大宽度: {max_width:.2f}cm")
        print(f"宽度比例: {max_width/min_width:.1f}:1")
        
        # 评估改进效果
        print("\n📊 改进效果评估:")
        
        # 检查最小宽度
        if min_width >= 0.8:
            print(f"✅ 最小宽度达标: {min_width:.2f}cm >= 0.8cm")
        else:
            print(f"❌ 最小宽度不足: {min_width:.2f}cm < 0.8cm")
        
        # 检查double-action宽度
        double_action_width = None
        for token, width in zip(tokens, colwidths):
            if token == "double-action":
                double_action_width = width.cm
                break
        
        if double_action_width:
            if double_action_width >= 3.5:
                print(f"✅ double-action宽度充足: {double_action_width:.2f}cm")
            else:
                print(f"❌ double-action宽度不足: {double_action_width:.2f}cm")
        
        # 检查宽度分布均衡性
        width_ratio = max_width / min_width
        if width_ratio <= 3.0:
            print(f"✅ 宽度分布均衡: 比例 {width_ratio:.1f}:1")
        elif width_ratio <= 5.0:
            print(f"⚠️  宽度分布一般: 比例 {width_ratio:.1f}:1")
        else:
            print(f"❌ 宽度分布不均: 比例 {width_ratio:.1f}:1")
        
        # 检查过窄单词数量
        narrow_count = sum(1 for w in colwidths if w.cm < 0.8)
        if narrow_count == 0:
            print(f"✅ 无过窄单词")
        else:
            print(f"❌ 有{narrow_count}个过窄单词")
        
    except Exception as e:
        print(f"❌ 算法执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    root.destroy()

def compare_algorithms():
    """对比新旧算法效果"""
    print("\n" + "=" * 80)
    print("新旧算法效果对比")
    print("=" * 80)
    
    # 这里我们可以手动模拟旧算法的结果进行对比
    old_results = {
        'The': 0.95, "revolver's": 2.20, 'double-action': 4.00, 'trigger': 1.79,
        'allows': 1.58, 'both': 1.16, 'cocking': 1.79, 'and': 0.95,
        'firing': 1.58, 'in': 0.74, 'one': 0.95, 'motion.': 1.65
    }
    
    print("旧算法问题:")
    print("- double-action: 4.00cm (足够)")
    print("- 但4个单词 < 1.0cm: The(0.95), and(0.95), in(0.74), one(0.95)")
    print("- 总宽度: 19.34cm")
    print("- 宽度比例: 5.4:1 (不均衡)")
    
    print("\n新算法目标:")
    print("- 保持double-action足够宽度避免换行")
    print("- 确保所有单词 >= 0.8cm")
    print("- 更均衡的宽度分布")
    print("- 控制总宽度在合理范围")

if __name__ == "__main__":
    test_balanced_width_algorithm()
    compare_algorithms()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print("如果新算法测试通过，说明列宽分配问题已解决！")
    print("期望效果:")
    print("1. double-action 不再换行")
    print("2. 其他单词有足够的显示空间")
    print("3. 整体表格布局更加美观均衡")
