#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from docx import Document
from docx.shared import Pt
import tkinter as tk
from oringin import WordAssistantApp

def create_test_document_with_superscript():
    """创建包含上标的测试文档"""
    doc = Document()
    
    # 创建包含上标的段落：Calculate 5² using the power rule.
    para = doc.add_paragraph()
    
    # 添加 "Calculate "
    run1 = para.add_run("Calculate ")
    run1.font.name = "Georgia"
    run1.font.size = Pt(12)
    
    # 添加 "5"
    run2 = para.add_run("5")
    run2.font.name = "Georgia"
    run2.font.size = Pt(12)
    
    # 添加上标 "²"
    run3 = para.add_run("²")
    run3.font.name = "Georgia"
    run3.font.size = Pt(12)
    run3.font.superscript = True  # 设置为上标
    
    # 添加 " using the power rule."
    run4 = para.add_run(" using the power rule.")
    run4.font.name = "Georgia"
    run4.font.size = Pt(12)
    
    return doc, para

def test_superscript_detection():
    """测试上标检测功能"""
    print("=== 测试上标检测功能 ===")
    
    # 创建测试文档
    doc, para = create_test_document_with_superscript()
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    
    # 获取段落文本
    text = para.text
    print(f"段落文本: '{text}'")
    
    # 显示runs信息
    print("\nRuns信息:")
    for i, run in enumerate(para.runs):
        print(f"  Run {i}: '{run.text}' - 上标: {run.font.superscript}, 下标: {run.font.subscript}")
    
    # 测试字符定位
    print("\n=== 测试字符定位 ===")
    test_words = ["Calculate", "5", "²", "5²", "using", "power"]
    
    for word in test_words:
        position = app.find_word_position_in_text(word, text, 0)
        print(f"单词 '{word}': 位置 {position}")
        
        if position != -1:
            format_info = app.get_format_at_position(para, position, word)
            print(f"  格式信息: 字体={format_info['font_name']}, 大小={format_info['font_size']}, "
                  f"上标={format_info['is_superscript']}, 下标={format_info['is_subscript']}")
    
    # 测试分词结果
    from oringin import extract_tokens
    tokens = extract_tokens(text)
    print(f"\n分词结果: {tokens}")
    
    # 测试每个token的格式检测
    print("\n=== 测试每个token的格式检测 ===")
    ptr = 0
    for token in tokens:
        position = app.find_word_position_in_text(token, text, ptr)
        if position != -1:
            format_info = app.get_format_at_position(para, position, token)
            print(f"Token '{token}': 位置{position}, 上标={format_info['is_superscript']}, 下标={format_info['is_subscript']}")
            ptr = position + len(token)
        else:
            print(f"Token '{token}': 未找到位置")
    
    root.destroy()

def test_combined_superscript_token():
    """测试组合的上标token（如5²）"""
    print("\n=== 测试组合上标token ===")
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    
    # 模拟包含5²的文本
    text = "Calculate 5² using the power rule."
    
    # 创建模拟的段落，其中5²是一个组合token
    from unittest.mock import Mock
    
    mock_para = Mock()
    
    # 模拟run1: "Calculate "
    mock_run1 = Mock()
    mock_run1.text = "Calculate "
    mock_run1.font.name = "Georgia"
    mock_run1.font.size.pt = 12
    mock_run1.font.superscript = False
    mock_run1.font.subscript = False
    
    # 模拟run2: "5²" (包含上标的组合)
    mock_run2 = Mock()
    mock_run2.text = "5²"
    mock_run2.font.name = "Georgia"
    mock_run2.font.size.pt = 12
    mock_run2.font.superscript = True  # 整个run都是上标
    mock_run2.font.subscript = False
    
    # 模拟run3: " using the power rule."
    mock_run3 = Mock()
    mock_run3.text = " using the power rule."
    mock_run3.font.name = "Georgia"
    mock_run3.font.size.pt = 12
    mock_run3.font.superscript = False
    mock_run3.font.subscript = False
    
    mock_para.runs = [mock_run1, mock_run2, mock_run3]
    
    # 测试5²的检测
    position = app.find_word_position_in_text("5²", text, 0)
    print(f"查找 '5²': 位置 {position}")
    
    if position != -1:
        format_info = app.get_format_at_position(mock_para, position, "5²")
        print(f"格式信息: 上标={format_info['is_superscript']}, 下标={format_info['is_subscript']}")
    
    # 测试单独的5的检测
    position5 = app.find_word_position_in_text("5", text, 0)
    print(f"查找 '5': 位置 {position5}")
    
    if position5 != -1:
        format_info5 = app.get_format_at_position(mock_para, position5, "5")
        print(f"格式信息: 上标={format_info5['is_superscript']}, 下标={format_info5['is_subscript']}")
    
    root.destroy()

if __name__ == "__main__":
    test_superscript_detection()
    test_combined_superscript_token()
    
    print("\n=== 总结 ===")
    print("✅ 上标检测功能已测试")
    print("✅ 字符定位功能已验证")
    print("✅ 格式信息提取已确认")
    print("\n如果原文档中确实有上标，应该能够正确检测和保持。")
    print("如果结果文件中没有显示上标，可能是原文档中本来就没有上标格式。")
