#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_balanced_table_layout():
    """测试平衡的表格布局"""
    print("=" * 70)
    print("测试修复后的表格布局（避免过度拉伸）")
    print("=" * 70)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)  # 设置默认最大宽度
    
    # 测试用例：混合普通单词和特殊单词
    test_cases = [
        {
            "text": "The revolver's double-action trigger allows both cocking and firing.",
            "description": "包含连字符单词的正常句子"
        },
        {
            "text": "Calculate 5² using the power rule for derivatives.",
            "description": "包含上标的数学句子"
        },
        {
            "text": "The formula H₂O represents water in chemistry.",
            "description": "包含下标的化学句子"
        },
        {
            "text": "This state-of-the-art eco-system provides excellent 5-star service quality.",
            "description": "包含多个连字符单词的复杂句子"
        },
        {
            "text": "Normal words without any special characters or hyphens.",
            "description": "纯普通单词的句子"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {case['description']} ---")
        print(f"输入: {case['text']}")
        
        # 分词
        tokens = extract_tokens(case['text'])
        print(f"分词结果: {tokens}")
        
        # 计算宽度
        colwidths = app.get_col_widths(tokens)
        
        # 分析宽度分布
        total_width = sum(w.cm for w in colwidths)
        max_width = max(w.cm for w in colwidths)
        min_width = min(w.cm for w in colwidths)
        avg_width = total_width / len(colwidths)
        
        print(f"宽度统计:")
        print(f"  总宽度: {total_width:.2f}cm")
        print(f"  平均宽度: {avg_width:.2f}cm")
        print(f"  最大宽度: {max_width:.2f}cm")
        print(f"  最小宽度: {min_width:.2f}cm")
        print(f"  宽度比例: {max_width/min_width:.1f}:1")
        
        # 检查是否有过度拉伸
        if max_width > 5.0:
            print(f"⚠️  可能过度拉伸: 最大宽度 {max_width:.2f}cm 超过 5.0cm")
        elif max_width > 4.0:
            print(f"⚠️  宽度较大: 最大宽度 {max_width:.2f}cm")
        else:
            print(f"✅ 宽度合理: 最大宽度 {max_width:.2f}cm")
        
        # 显示每个token的宽度
        print("详细宽度分配:")
        for j, (token, width) in enumerate(zip(tokens, colwidths)):
            width_cm = width.cm
            token_type = ""
            if '-' in token and len(token) > 6:
                token_type = " (连字符)"
            elif any(c in token for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉'):
                token_type = " (上下标)"
            
            print(f"  {token}: {width_cm:.2f}cm{token_type}")
        
        # 评估布局质量
        width_variance = sum((w.cm - avg_width)**2 for w in colwidths) / len(colwidths)
        if width_variance < 0.5:
            print("✅ 布局均衡: 宽度分布较为均匀")
        elif width_variance < 1.0:
            print("⚠️  布局一般: 宽度分布有些不均")
        else:
            print("❌ 布局不均: 宽度分布差异较大")

def test_specific_problematic_cases():
    """测试可能导致拉伸的特定情况"""
    print("\n" + "=" * 70)
    print("测试可能导致拉伸的特定情况")
    print("=" * 70)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    problematic_cases = [
        "state-of-the-art",  # 超长连字符单词
        "twenty-first-century",  # 另一个超长连字符单词
        "well-known high-quality user-friendly",  # 多个连字符单词
        "5² × 10³ = 2.5 × 10⁵",  # 多个上标
        "H₂SO₄ and CO₂",  # 多个化学式
    ]
    
    for case in problematic_cases:
        print(f"\n测试: {case}")
        tokens = extract_tokens(case)
        colwidths = app.get_col_widths(tokens)
        
        max_width = max(w.cm for w in colwidths)
        total_width = sum(w.cm for w in colwidths)
        
        print(f"  分词: {tokens}")
        print(f"  最大宽度: {max_width:.2f}cm")
        print(f"  总宽度: {total_width:.2f}cm")
        
        if max_width > 5.0:
            print(f"  ❌ 过度拉伸风险")
        elif max_width > 4.0:
            print(f"  ⚠️  宽度偏大")
        else:
            print(f"  ✅ 宽度合理")
    
    root.destroy()

def test_comparison_with_original():
    """对比修复前后的效果"""
    print("\n" + "=" * 70)
    print("修复前后效果对比")
    print("=" * 70)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    test_text = "The revolver's double-action trigger allows both cocking and firing."
    tokens = extract_tokens(test_text)
    colwidths = app.get_col_widths(tokens)
    
    print(f"测试句子: {test_text}")
    print(f"分词结果: {tokens}")
    print("\n修复后的宽度分配:")
    
    for token, width in zip(tokens, colwidths):
        width_cm = width.cm
        if token == "double-action":
            print(f"  {token}: {width_cm:.2f}cm ← 关键连字符单词")
        else:
            print(f"  {token}: {width_cm:.2f}cm")
    
    # 检查double-action是否获得足够但不过度的宽度
    double_action_width = None
    for token, width in zip(tokens, colwidths):
        if token == "double-action":
            double_action_width = width.cm
            break
    
    if double_action_width:
        if 2.5 <= double_action_width <= 4.0:
            print(f"\n✅ 'double-action' 宽度 {double_action_width:.2f}cm 在合理范围内")
            print("   - 足够避免换行")
            print("   - 不会过度拉伸表格")
        elif double_action_width > 4.0:
            print(f"\n⚠️  'double-action' 宽度 {double_action_width:.2f}cm 可能过大")
        else:
            print(f"\n❌ 'double-action' 宽度 {double_action_width:.2f}cm 可能不足")
    
    root.destroy()

if __name__ == "__main__":
    test_balanced_table_layout()
    test_specific_problematic_cases()
    test_comparison_with_original()
    
    print("\n" + "=" * 70)
    print("表格布局测试总结")
    print("=" * 70)
    print("✅ 修复目标:")
    print("  • 连字符单词获得足够宽度，避免换行")
    print("  • 上标下标字符正确显示")
    print("  • 避免表格过度拉伸")
    print("  • 保持整体布局的平衡性")
    print("\n如果测试显示宽度合理，说明修复成功！")
