#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from oringin import extract_tokens

# 测试修复后的分词效果
test_cases = [
    "Calculate 5² using the power rule.",
    "The formula H₂O represents water.",
    "The revolver's double-action trigger.",
    "Plants grow at a rapid rate in tropical climate. The tax rate for high-income earners is 40%."
]

print("=== 测试修复后的分词效果 ===")
for i, text in enumerate(test_cases, 1):
    print(f"\n测试 {i}: {text}")
    tokens = extract_tokens(text)
    print(f"分词结果: {tokens}")
    
    # 检查特殊情况
    if '5²' in text:
        if '5²' in tokens:
            print("✅ 数字上标组合 '5²' 被正确保持")
        else:
            print("❌ 数字上标组合 '5²' 被分割")
    
    if 'H₂O' in text:
        if 'H₂O' in tokens:
            print("✅ 化学式 'H₂O' 被正确保持")
        else:
            print("❌ 化学式 'H₂O' 被分割")
    
    if 'double-action' in text:
        if 'double-action' in tokens:
            print("✅ 连字符单词 'double-action' 被正确保持")
        else:
            print("❌ 连字符单词 'double-action' 被分割")
    
    if 'high-income' in text:
        if 'high-income' in tokens:
            print("✅ 连字符单词 'high-income' 被正确保持")
        else:
            print("❌ 连字符单词 'high-income' 被分割")
