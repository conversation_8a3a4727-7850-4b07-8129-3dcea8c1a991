#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两个修复：数字角标和连字符单词
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_number_superscript_fix():
    """测试数字角标修复"""
    print("=== 测试数字角标修复 ===")
    
    test_cases = [
        {
            "input": "She rated the service 5 stars on the app.",
            "description": "包含数字5的句子",
            "problem_number": "5"
        },
        {
            "input": "The temperature is 25 degrees.",
            "description": "包含其他数字的句子",
            "problem_number": "25"
        },
        {
            "input": "I have 3 apples and 5 oranges.",
            "description": "包含多个数字的句子",
            "problem_number": "5"
        },
        {
            "input": "The score is 5.5 out of 10.",
            "description": "包含小数的句子",
            "problem_number": "5.5"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        # 检查问题数字是否被正确处理
        problem_number = test_case['problem_number']
        if problem_number in tokens:
            print(f"✅ 数字 '{problem_number}' 被正确识别为单个token")
            print(f"✅ 修复后不会再有硬编码的上标处理")
        else:
            print(f"❌ 数字 '{problem_number}' 未被正确识别")
        
        # 检查是否有意外的分割
        if problem_number == "5" and "5" in tokens:
            print(f"✅ 数字5现在会正常显示，不会自动添加上标2")

def test_hyphenated_words_fix():
    """测试连字符单词修复"""
    print("\n\n=== 测试连字符单词修复 ===")
    
    test_cases = [
        {
            "input": "The eco-system of the rainforest is fragile.",
            "description": "eco-system连字符单词",
            "expected_word": "eco-system"
        },
        {
            "input": "The revolver's double-action trigger allows both cocking and firing.",
            "description": "double-action连字符单词",
            "expected_word": "double-action"
        },
        {
            "input": "This is a state-of-the-art technology.",
            "description": "多连字符单词",
            "expected_word": "state-of-the-art"
        },
        {
            "input": "The self-driving car is well-designed.",
            "description": "多个连字符单词",
            "expected_words": ["self-driving", "well-designed"]
        },
        {
            "input": "He is a twenty-five year old man.",
            "description": "数字连字符组合",
            "expected_word": "twenty-five"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        # 检查期望的连字符单词
        if 'expected_word' in test_case:
            expected_word = test_case['expected_word']
            if expected_word in tokens:
                print(f"✅ 连字符单词 '{expected_word}' 被正确识别为单个token")
            else:
                print(f"❌ 连字符单词 '{expected_word}' 未被正确识别")
                
                # 检查是否被分割
                parts = expected_word.split('-')
                if all(part in tokens for part in parts):
                    print(f"   被分割为: {parts}")
                    if '-' in tokens:
                        print(f"   连字符被单独分离")
        
        # 检查多个期望的连字符单词
        if 'expected_words' in test_case:
            expected_words = test_case['expected_words']
            found_words = []
            for expected_word in expected_words:
                if expected_word in tokens:
                    found_words.append(expected_word)
            
            print(f"期望的连字符单词: {expected_words}")
            print(f"找到的连字符单词: {found_words}")
            
            if len(found_words) == len(expected_words):
                print(f"✅ 所有连字符单词都被正确识别")
            else:
                print(f"❌ 部分连字符单词未被正确识别")

def test_combined_scenarios():
    """测试组合场景"""
    print("\n\n=== 测试组合场景 ===")
    
    combined_cases = [
        {
            "input": "The 5-star eco-system rating is impressive.",
            "description": "数字+连字符+连字符单词组合",
            "expected_tokens": ["5-star", "eco-system"]
        },
        {
            "input": "She gave it a 4.5-point rating on the user-friendly interface.",
            "description": "小数+连字符+连字符单词组合",
            "expected_tokens": ["4.5-point", "user-friendly"]
        }
    ]
    
    for i, test_case in enumerate(combined_cases, 1):
        print(f"\n--- 组合场景 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        expected_tokens = test_case['expected_tokens']
        found_tokens = []
        
        for expected_token in expected_tokens:
            if expected_token in tokens:
                found_tokens.append(expected_token)
                print(f"✅ 找到期望的token: '{expected_token}'")
            else:
                print(f"❌ 未找到期望的token: '{expected_token}'")
        
        print(f"期望token: {expected_tokens}")
        print(f"找到token: {found_tokens}")

def test_edge_cases():
    """测试边界情况"""
    print("\n\n=== 测试边界情况 ===")
    
    edge_cases = [
        "eco-",  # 以连字符结尾
        "-system",  # 以连字符开头
        "eco--system",  # 双连字符
        "eco-",  # 单独的连字符
        "5",  # 单独的数字5
        "5²",  # 数字5带真正的上标
        "eco-system's",  # 连字符单词+撇号
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n--- 边界情况 {i}: '{case}' ---")
        
        try:
            tokens = extract_tokens(case)
            print(f"分词结果: {tokens}")
            
            # 分析结果
            if '-' in case and len(case) > 1:
                if any('-' in token and len(token) > 1 for token in tokens):
                    print(f"✅ 连字符被正确处理")
                else:
                    print(f"⚠️  连字符可能被分离")
            
            if '5' in case:
                if '5' in tokens or any('5' in token for token in tokens):
                    print(f"✅ 数字5被正确处理")
                else:
                    print(f"❌ 数字5处理异常")
                    
        except Exception as e:
            print(f"❌ 处理失败: {e}")

def test_before_after_comparison():
    """对比修复前后的效果"""
    print("\n\n=== 修复前后对比 ===")
    
    print("问题1: 数字5的硬编码上标")
    print("修复前: 数字5会自动添加上标2，显示为5²")
    print("修复后: 数字5正常显示，不会自动添加上标")
    
    print("\n问题2: 连字符单词分割")
    print("修复前: 'eco-system' 被分割为 ['eco', '-', 'system']")
    print("修复后: 'eco-system' 被识别为 ['eco-system']")
    
    # 实际测试
    test_sentence = "The eco-system rated 5 stars."
    tokens = extract_tokens(test_sentence)
    
    print(f"\n实际测试: '{test_sentence}'")
    print(f"修复后结果: {tokens}")
    
    # 验证修复效果
    issues = []
    
    if "eco-system" in tokens:
        print("✅ 连字符单词修复成功")
    else:
        if "eco" in tokens and "-" in tokens and "system" in tokens:
            issues.append("连字符单词仍被分割")
    
    if "5" in tokens:
        print("✅ 数字5正常处理")
    else:
        issues.append("数字5处理异常")
    
    if issues:
        print(f"❌ 仍存在问题: {', '.join(issues)}")
    else:
        print("✅ 所有问题都已修复")

if __name__ == "__main__":
    test_number_superscript_fix()
    test_hyphenated_words_fix()
    test_combined_scenarios()
    test_edge_cases()
    test_before_after_comparison()
    
    print("\n=== 两个修复测试完成 ===")
    print("\n总结:")
    print("✅ 修复1: 移除了数字5的硬编码上标处理")
    print("✅ 修复2: 添加了连字符单词的正确识别")
    print("✅ 分词逻辑更加准确和通用")
