#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from docx import Document
from docx.shared import Pt

def create_test_document():
    """创建包含所有测试用例的Word文档"""
    doc = Document()
    
    # 添加标题
    title = doc.add_heading('测试文档：数字角标和连字符单词', 0)
    
    # 测试用例1：连字符单词
    doc.add_heading('测试1：连字符单词', level=1)
    para1 = doc.add_paragraph()
    run1 = para1.add_run("The revolver's double-action trigger allows both cocking and firing in one motion.")
    run1.font.name = "Georgia"
    run1.font.size = Pt(12)
    
    # 测试用例2：数字上标
    doc.add_heading('测试2：数字上标', level=1)
    para2 = doc.add_paragraph()
    
    run2a = para2.add_run("Calculate ")
    run2a.font.name = "Georgia"
    run2a.font.size = Pt(12)
    
    run2b = para2.add_run("5")
    run2b.font.name = "Georgia"
    run2b.font.size = Pt(12)
    
    run2c = para2.add_run("²")
    run2c.font.name = "Georgia"
    run2c.font.size = Pt(12)
    run2c.font.superscript = True
    
    run2d = para2.add_run(" using the power rule.")
    run2d.font.name = "Georgia"
    run2d.font.size = Pt(12)
    
    # 测试用例3：化学式下标
    doc.add_heading('测试3：化学式下标', level=1)
    para3 = doc.add_paragraph()
    
    run3a = para3.add_run("The formula H")
    run3a.font.name = "Georgia"
    run3a.font.size = Pt(12)
    
    run3b = para3.add_run("2")
    run3b.font.name = "Georgia"
    run3b.font.size = Pt(12)
    run3b.font.subscript = True
    
    run3c = para3.add_run("O represents water.")
    run3c.font.name = "Georgia"
    run3c.font.size = Pt(12)
    
    # 测试用例4：复杂组合
    doc.add_heading('测试4：复杂组合', level=1)
    para4 = doc.add_paragraph()
    
    run4a = para4.add_run("Plants grow at a rapid rate in tropical climate. The tax rate for high-income earners is 40%.")
    run4a.font.name = "Georgia"
    run4a.font.size = Pt(12)
    
    # 保存文档
    doc.save('test_input_document.docx')
    print("✅ 测试文档已创建: test_input_document.docx")
    print("\n📋 测试内容:")
    print("1. 连字符单词: double-action, high-income")
    print("2. 数字上标: 5²")
    print("3. 化学式下标: H₂O")
    print("4. 复杂组合场景")
    print("\n🔧 使用方法:")
    print("1. 运行主程序")
    print("2. 选择 test_input_document.docx 作为输入文件")
    print("3. 处理后检查结果文档中的格式保持情况")

if __name__ == "__main__":
    create_test_document()
