#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def test_natural_algorithm():
    """测试自然宽度算法效果"""
    print("=" * 80)
    print("测试自然宽度分配算法")
    print("=" * 80)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试用例
    test_cases = [
        {
            "text": "The power outage lasted hours.",
            "description": "基础测试句子",
            "previous": 6.20  # 平衡算法的结果
        },
        {
            "text": "I rate this book highly.",
            "description": "短单词句子",
            "previous": 5.70
        },
        {
            "text": "The revolver's double-action trigger.",
            "description": "连字符单词测试",
            "previous": 8.25
        },
        {
            "text": "Calculate 5² using the power rule.",
            "description": "上标字符测试",
            "previous": 7.50
        },
        {
            "text": "The microscope has a magnificationpower of 1000x.",
            "description": "长单词测试（图片中的问题句子）",
            "previous": None  # 新测试
        }
    ]
    
    total_previous = 0
    total_natural = 0
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        print(f"句子: {case['text']}")
        
        tokens = extract_tokens(case['text'])
        
        try:
            colwidths = app.get_col_widths(tokens)
            
            print(f"分词: {tokens}")
            print("自然宽度分配:")
            
            natural_total = sum(w.cm for w in colwidths)
            previous_total = case['previous']
            
            for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
                width_cm = width.cm
                char_count = len(token)
                char_density = width_cm / char_count if char_count > 0 else 0
                
                # 评估自然度
                expected_width = char_count * 0.12 + 0.15
                if char_count == 1:
                    expected_width = max(expected_width, 0.4)
                elif char_count == 2:
                    expected_width = max(expected_width, 0.5)
                
                # 检查是否接近期望值
                if abs(width_cm - expected_width) <= 0.1:
                    status = "✅ 自然"
                elif width_cm > expected_width + 0.2:
                    status = "⚠️  偏宽"
                elif width_cm < expected_width - 0.1:
                    status = "⚠️  偏窄"
                else:
                    status = "✅ 合理"
                
                print(f"  {i:2d}. {token:18s}: {width_cm:5.2f}cm ({char_count}字符, 密度{char_density:.3f}) {status}")
            
            print(f"\n📊 效果对比:")
            if previous_total:
                print(f"  平衡算法: {previous_total:.2f}cm")
                print(f"  自然算法: {natural_total:.2f}cm")
                improvement = previous_total - natural_total
                improvement_percent = (improvement / previous_total) * 100
                print(f"  改进: {improvement:+.2f}cm ({improvement_percent:+.1f}%)")
                
                total_previous += previous_total
                total_natural += natural_total
            else:
                print(f"  自然算法: {natural_total:.2f}cm")
                total_natural += natural_total
            
            # 检查关键指标
            min_width = min(w.cm for w in colwidths)
            max_width = max(w.cm for w in colwidths)
            width_ratio = max_width / min_width
            
            print(f"  最小宽度: {min_width:.2f}cm")
            print(f"  最大宽度: {max_width:.2f}cm")
            print(f"  宽度比例: {width_ratio:.1f}:1")
            
            # 检查特殊单词
            for token, width in zip(tokens, colwidths):
                if token == "double-action":
                    if width.cm >= 2.8:
                        print(f"  ✅ double-action: {width.cm:.2f}cm (不换行)")
                    else:
                        print(f"  ❌ double-action: {width.cm:.2f}cm (可能换行)")
                elif any(c in token for c in '²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉'):
                    print(f"  ✅ 上下标 {token}: {width.cm:.2f}cm")
                elif len(token) > 15:
                    print(f"  📏 长单词 {token}: {width.cm:.2f}cm")
            
        except Exception as e:
            print(f"❌ 算法执行出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 总体效果评估
    if total_previous > 0:
        overall_improvement = total_previous - total_natural
        overall_improvement_percent = (overall_improvement / total_previous) * 100
        
        print(f"\n" + "=" * 80)
        print("总体效果评估")
        print("=" * 80)
        print(f"总宽度对比:")
        print(f"  平衡算法: {total_previous:.2f}cm")
        print(f"  自然算法: {total_natural:.2f}cm")
        print(f"  总改进: {overall_improvement:+.2f}cm ({overall_improvement_percent:+.1f}%)")
        
        if overall_improvement > 0:
            print("✅ 自然算法更紧凑，更接近源文档")
        else:
            print("⚠️  自然算法略宽，但可能更自然")

def test_character_density():
    """测试字符密度的一致性"""
    print("\n" + "=" * 80)
    print("字符密度一致性测试")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 测试不同长度的单词
    test_words = ["I", "am", "the", "book", "power", "outage", "trigger", "calculate", "magnification"]
    
    print("字符密度分析:")
    print(f"{'单词':<15} {'字符数':<6} {'宽度(cm)':<10} {'密度':<10} {'理想密度':<10} {'评价'}")
    print("-" * 70)
    
    densities = []
    
    for word in test_words:
        tokens = [word]  # 单个单词测试
        colwidths = app.get_col_widths(tokens)
        
        if colwidths:
            width_cm = colwidths[0].cm
            char_count = len(word)
            density = width_cm / char_count
            
            # 理想密度计算
            ideal_width = char_count * 0.12 + 0.15
            if char_count == 1:
                ideal_width = max(ideal_width, 0.4)
            elif char_count == 2:
                ideal_width = max(ideal_width, 0.5)
            ideal_density = ideal_width / char_count
            
            densities.append(density)
            
            # 评价
            if abs(density - ideal_density) <= 0.02:
                rating = "✅ 优秀"
            elif abs(density - ideal_density) <= 0.05:
                rating = "✅ 良好"
            else:
                rating = "⚠️  偏差"
            
            print(f"{word:<15} {char_count:<6} {width_cm:<10.2f} {density:<10.3f} {ideal_density:<10.3f} {rating}")
    
    # 密度一致性分析
    if densities:
        avg_density = sum(densities) / len(densities)
        max_density = max(densities)
        min_density = min(densities)
        density_variance = max_density - min_density
        
        print(f"\n密度统计:")
        print(f"  平均密度: {avg_density:.3f}cm/字符")
        print(f"  密度范围: {min_density:.3f} - {max_density:.3f}cm/字符")
        print(f"  密度方差: {density_variance:.3f}cm/字符")
        
        if density_variance <= 0.1:
            print("✅ 密度一致性优秀")
        elif density_variance <= 0.2:
            print("✅ 密度一致性良好")
        else:
            print("⚠️  密度一致性需要改进")
    
    root.destroy()

def test_problematic_cases():
    """测试图片中的问题案例"""
    print("\n" + "=" * 80)
    print("问题案例专项测试")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    # 图片中的问题句子
    problem_cases = [
        "The microscope has a magnificationpower of 1000x.",
        "Her persuasivepower convinced the team to change the strategy.",
        "The hotel chargescompetitive rates."
    ]
    
    for text in problem_cases:
        print(f"\n问题句子: {text}")
        
        tokens = extract_tokens(text)
        colwidths = app.get_col_widths(tokens)
        
        print(f"分词结果: {tokens}")
        print("自然宽度分配:")
        
        total_width = sum(w.cm for w in colwidths)
        
        for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
            width_cm = width.cm
            char_count = len(token)
            
            # 检查是否解决了过宽过窄问题
            if char_count <= 3 and width_cm <= 0.8:
                status = "✅ 紧凑合理"
            elif char_count <= 6 and width_cm <= 1.2:
                status = "✅ 适中"
            elif char_count > 10 and width_cm >= 1.5:
                status = "✅ 长单词充足"
            elif width_cm > 2.0:
                status = "⚠️  可能过宽"
            elif width_cm < 0.3:
                status = "⚠️  可能过窄"
            else:
                status = "✅ 合理"
            
            print(f"  {i:2d}. {token:20s}: {width_cm:5.2f}cm ({char_count}字符) {status}")
        
        print(f"总宽度: {total_width:.2f}cm")
    
    root.destroy()

if __name__ == "__main__":
    test_natural_algorithm()
    test_character_density()
    test_problematic_cases()
    
    print("\n" + "=" * 80)
    print("自然算法测试总结")
    print("=" * 80)
    print("🎯 自然算法目标:")
    print("1. 基于字符数的精确宽度计算")
    print("2. 更接近源文档的自然间距")
    print("3. 消除过宽和过窄的极端情况")
    print("4. 保持字符密度的一致性")
    print("5. 维持所有特殊功能")
    print("\n如果测试显示一致的字符密度和自然的宽度分配，说明算法成功！")
