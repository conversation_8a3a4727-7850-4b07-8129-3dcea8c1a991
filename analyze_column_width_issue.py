#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from oringin import extract_tokens, WordAssistantApp

def analyze_current_width_distribution():
    """分析当前的列宽分配问题"""
    print("=" * 80)
    print("分析当前列宽分配问题")
    print("=" * 80)
    
    # 创建应用实例
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)  # 默认最大宽度
    
    # 测试句子
    test_text = "The revolver's double-action trigger allows both cocking and firing in one motion."
    tokens = extract_tokens(test_text)
    colwidths = app.get_col_widths(tokens)
    
    print(f"测试句子: {test_text}")
    print(f"分词结果: {tokens}")
    print(f"Token数量: {len(tokens)}")
    print()
    
    # 分析宽度分配
    total_width = sum(w.cm for w in colwidths)
    print("📊 当前宽度分配:")
    print(f"{'序号':<4} {'Token':<20} {'宽度(cm)':<10} {'占比(%)':<8} {'问题'}")
    print("-" * 70)
    
    for i, (token, width) in enumerate(zip(tokens, colwidths), 1):
        width_cm = width.cm
        percentage = (width_cm / total_width) * 100
        
        # 识别问题
        issues = []
        if width_cm < 0.8:
            issues.append("过窄")
        if width_cm > 4.0:
            issues.append("过宽")
        if len(token) > 8 and width_cm < 2.0:
            issues.append("长词压缩")
        
        issue_str = ", ".join(issues) if issues else "正常"
        
        print(f"{i:<4} {token:<20} {width_cm:<10.2f} {percentage:<8.1f} {issue_str}")
    
    print("-" * 70)
    print(f"总宽度: {total_width:.2f}cm")
    
    # 分析问题
    print("\n🔍 问题分析:")
    narrow_tokens = [(token, width.cm) for token, width in zip(tokens, colwidths) if width.cm < 1.0]
    wide_tokens = [(token, width.cm) for token, width in zip(tokens, colwidths) if width.cm > 3.0]
    
    if narrow_tokens:
        print(f"❌ 过窄单词 (<1.0cm): {len(narrow_tokens)}个")
        for token, width in narrow_tokens:
            print(f"   - {token}: {width:.2f}cm")
    
    if wide_tokens:
        print(f"⚠️  过宽单词 (>3.0cm): {len(wide_tokens)}个")
        for token, width in wide_tokens:
            print(f"   - {token}: {width:.2f}cm")
    
    # 计算理想分配
    print("\n💡 理想分配建议:")
    ideal_avg = total_width / len(tokens)
    print(f"平均宽度: {ideal_avg:.2f}cm")
    
    # 建议最小和最大宽度
    min_width = 0.8  # 最小宽度
    max_normal_width = 2.5  # 普通单词最大宽度
    
    print(f"建议最小宽度: {min_width}cm")
    print(f"建议普通单词最大宽度: {max_normal_width}cm")
    print(f"特殊单词(连字符)可以更宽")
    
    root.destroy()

def test_width_balance_scenarios():
    """测试不同场景下的宽度平衡"""
    print("\n" + "=" * 80)
    print("测试不同场景下的宽度平衡")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    app = WordAssistantApp(root)
    app.max_width_var.set(2.2)
    
    test_cases = [
        {
            "text": "The cat sat on the mat.",
            "description": "短单词句子"
        },
        {
            "text": "The revolver's double-action trigger.",
            "description": "包含长连字符单词"
        },
        {
            "text": "Calculate 5² using the power rule.",
            "description": "包含上标"
        },
        {
            "text": "The state-of-the-art technology provides excellent performance.",
            "description": "超长连字符单词"
        }
    ]
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        print(f"句子: {case['text']}")
        
        tokens = extract_tokens(case['text'])
        colwidths = app.get_col_widths(tokens)
        
        total_width = sum(w.cm for w in colwidths)
        min_width = min(w.cm for w in colwidths)
        max_width = max(w.cm for w in colwidths)
        avg_width = total_width / len(colwidths)
        
        print(f"Token数: {len(tokens)}, 总宽: {total_width:.1f}cm, "
              f"最小: {min_width:.2f}cm, 最大: {max_width:.2f}cm, 平均: {avg_width:.2f}cm")
        
        # 评估平衡性
        width_ratio = max_width / min_width if min_width > 0 else float('inf')
        if width_ratio > 5.0:
            print("❌ 宽度分布极不均衡")
        elif width_ratio > 3.0:
            print("⚠️  宽度分布不够均衡")
        else:
            print("✅ 宽度分布相对均衡")
    
    root.destroy()

def propose_solution():
    """提出解决方案"""
    print("\n" + "=" * 80)
    print("列宽分配优化方案")
    print("=" * 80)
    
    print("🎯 优化目标:")
    print("1. 保持连字符单词足够宽度避免换行")
    print("2. 确保普通单词最小宽度 >= 0.8cm")
    print("3. 实现更均衡的宽度分布")
    print("4. 控制总表格宽度在合理范围")
    
    print("\n🔧 解决方案:")
    print("1. 实现两阶段宽度分配:")
    print("   - 第一阶段: 为特殊单词(连字符、上下标)分配足够宽度")
    print("   - 第二阶段: 在剩余空间内均匀分配普通单词宽度")
    
    print("\n2. 设置宽度约束:")
    print("   - 最小宽度: 0.8cm (确保可读性)")
    print("   - 普通单词最大宽度: 2.5cm (避免浪费)")
    print("   - 连字符单词: 根据需要可达4-6cm")
    
    print("\n3. 动态调整机制:")
    print("   - 如果总宽度超限，按比例缩减非关键单词")
    print("   - 如果总宽度过小，适当增加普通单词宽度")
    
    print("\n4. 优先级策略:")
    print("   - 高优先级: 连字符单词、上下标单词")
    print("   - 中优先级: 长单词(>6字符)")
    print("   - 低优先级: 短单词(<4字符)")

if __name__ == "__main__":
    analyze_current_width_distribution()
    test_width_balance_scenarios()
    propose_solution()
    
    print("\n" + "=" * 80)
    print("总结")
    print("=" * 80)
    print("当前问题: double-action获得4.0cm宽度，但其他单词被过度压缩")
    print("解决方向: 实现智能的两阶段宽度分配算法")
    print("预期效果: 既保证特殊单词不换行，又确保整体布局均衡美观")
